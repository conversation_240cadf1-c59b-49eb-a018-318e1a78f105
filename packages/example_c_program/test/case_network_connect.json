{"name": "网络连接测试", "description": "测试network_connect函数的各种场景", "setup": [{"action": "exec", "command": ["example_client", "test.example.com", "8080"]}], "cases": [{"name": "连接成功", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "breakpoints": {"functions": ["network_send"]}, "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["********"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [5]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "********"}}}, {"name": "连接失败", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["********"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [-1]}}]}], "verify": {"command": "echo 'Connection failed test'", "expect_exit_code": 0}}, {"name": "无效IP地址连接", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["999.999.999.999"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [-1]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "999.999.999.999"}}}], "cleanup": [{"exit": "example_client"}]}