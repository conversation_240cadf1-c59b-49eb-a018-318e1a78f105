{"name": "错误处理测试", "description": "测试各种错误场景的处理", "setup": [{"action": "exec", "command": ["example_client", "invalid.domain.test", "9999"]}], "cases": [{"name": "DNS解析错误", "targets": [{"process": "example_client", "aspects": ["resolve_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [-1]}}]}], "verify": {"command": "echo 'DNS resolution error test'", "expect_exit_code": 0}}, {"name": "网络连接错误", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["*************"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [-1]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "*************"}}}, {"name": "网络发送错误", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["*************"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [7]}}, {"function": "network_recv", "return": {"type": "int", "values": [5]}, "args": [{"name": "buffer", "type": "ptr_out", "values": ["test"]}]}, {"function": "network_send", "return": {"type": "int", "values": [-1]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "*************"}}}, {"name": "网络接收错误", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["*************"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [8]}}, {"function": "network_recv", "return": {"type": "int", "values": [-1]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "*************"}}}, {"name": "连续错误处理", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["*************"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [9]}}, {"function": "network_recv", "return": {"type": "int", "values": [-1, -1, 10]}, "args": [{"name": "buffer", "type": "ptr_out", "values": ["", "", "recovery"]}]}, {"function": "network_send", "return": {"type": "int", "values": [-1, 15]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "*************"}}}], "cleanup": [{"exit": "example_client"}]}