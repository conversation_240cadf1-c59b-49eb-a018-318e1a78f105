{"name": "DNS解析测试", "description": "测试DNS解析功能", "setup": [{"action": "exec", "command": ["example_client"]}], "cases": [{"name": "DNS解析成功", "targets": [{"process": "example_client", "aspects": ["resolve_aspect"], "breakpoints": {"functions": ["network_connect"]}, "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["***********"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [10]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "***********"}}}, {"name": "DNS解析失败", "targets": [{"process": "example_client", "aspects": ["resolve_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [-1]}}]}], "verify": {"command": "echo 'DNS resolution failed test'", "expect_exit_code": 0}}], "cleanup": [{"exit": "example_client"}]}