{"name": "数据传输测试", "description": "测试network_send和network_recv函数的各种场景", "setup": [{"action": "exec", "command": ["example_client", "localhost", "8080"]}], "cases": [{"name": "正常数据发送", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "breakpoints": {"functions": ["network_recv"]}, "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["127.0.0.1"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [3]}}, {"function": "network_send", "return": {"type": "int", "values": [50]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "127.0.0.1"}}}, {"name": "正常数据接收", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "breakpoints": {"functions": ["network_send"]}, "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["127.0.0.1"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [4]}}, {"function": "network_recv", "return": {"type": "int", "values": [20]}, "args": [{"name": "buffer", "type": "ptr_out", "values": ["Hello from server"]}]}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "127.0.0.1"}}}, {"name": "发送失败", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["127.0.0.1"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [5]}}, {"function": "network_recv", "return": {"type": "int", "values": [10]}, "args": [{"name": "buffer", "type": "ptr_out", "values": ["test"]}]}, {"function": "network_send", "return": {"type": "int", "values": [-1]}}]}], "verify": {"command": "echo 'Send failed test'", "expect_exit_code": 0}}, {"name": "接收失败", "targets": [{"process": "example_client", "aspects": ["resolve_aspect", "network_aspect"], "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["127.0.0.1"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [6]}}, {"function": "network_recv", "return": {"type": "int", "values": [-1]}}]}], "verify": {"command": "echo 'Receive failed test'", "expect_exit_code": 0}}], "cleanup": [{"exit": "example_client"}]}