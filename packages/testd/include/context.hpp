/**
 * @file context.hpp
 * @brief 全局上下文类定义 - C++11版本
 */

#ifndef TESTD_CONTEXT_HPP
#define TESTD_CONTEXT_HPP

#include "testd.hpp"
#include <json-c/json_object.h>
#include <ostream>
#include <unordered_map>
#include <uuid/uuid.h>
#include <sys/select.h>

namespace testd {

/**
 * @brief 测试控制端配置结构
 */
class Config {
public:
    Config();
    ~Config() = default;

    // 禁止拷贝和赋值
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;

    // 解析命令行参数
    bool parseArgs(int argc, char** argv, CommandType& cmdType,
                  std::vector<std::string>& testFiles,
                  pid_t& targetPid, std::string& functionName, struct json_object*& params);

    // 打印帮助信息
    static void printHelp();

    // 打印版本信息
    static void printVersion();

    // Getters
    int getTcpPort() const { return tcpPort; }
    bool isVerbose() const { return verbose; }
    const std::string& getConfigFile() const { return configFile; }
    const std::string& getOutputFile() const { return outputFile; }
    const std::string& getSysroot() const { return sysroot; }

    // Setters
    void setTcpPort(int port) { tcpPort = port; }
    void setVerbose(bool v) { verbose = v; }
    void setConfigFile(const std::string& file) { configFile = file; }
    void setOutputFile(const std::string& file) { outputFile = file; }
    void setSysroot(const std::string& root) { sysroot = root; }

private:
    int tcpPort;                /**< TCP控制端口 */
    bool verbose;               /**< 详细输出模式 */
    std::string configFile;     /**< 配置文件路径 */
    std::string outputFile;     /**< 输出文件路径 */
    std::string sysroot;        /**< 系统根目录路径 */
};

using ClientMapType = std::unordered_map<std::string, std::shared_ptr<ClientConnection>>;

/**
 * @brief 被控端连接类
 */
class ClientConnection: public std::enable_shared_from_this<ClientConnection> {
    struct Private{};
public:
    ClientConnection(int socketFd, pid_t pid, const std::string& programName, Private);
    static inline std::shared_ptr<ClientConnection> create(int socketFd, pid_t pid, const std::string& programName) {
        return std::make_shared<ClientConnection>(socketFd, pid, programName, Private());
    }
    ~ClientConnection();

    // 禁止拷贝和赋值
    ClientConnection(const ClientConnection&) = delete;
    ClientConnection& operator=(const ClientConnection&) = delete;

    // Getters
    int getSocketFd() const { return socketFd; }
    pid_t getPid() const { return pid; }
    std::string getProgramName() const { return programName; }
    const std::string& getUUID() const { return uuid; }
    TestMode getMode() const { return mode; }


    // Setters
    void setPid(pid_t p) { pid = p; }
    void setProgramName(const std::string& name) { programName = name; }
    void setMode(TestMode m) { mode = m; }
    void setPipeFd(int index, int fd) { pipeFd[index] = fd; }

    void setIsChild(bool c) { isChild = c; }
    bool getIsWaitAttach() const { return pid <= 0; }

    void updateFdSet(fd_set& read_fds, int& max_fd) const;

    void updateClientData(fd_set &read_fds);
    void printClientData(std::ostream& os);

    void cleanup();

    static void updateClients(ClientMapType& clients, fd_set& read_fds);

private:
    int socketFd;               /**< Socket文件描述符 */
    int pipeFd[3];              /**< 管道文件描述符 */
    pid_t pid;                  /**< 进程ID */
    bool isChild;               /**< 是否为子进程 */
    int exitStatus;             /**< 退出状态 */
    std::string programName;    /**< 程序名称 */
    std::string uuid;           /**< 客户端唯一标识符 */
    TestMode mode;              /**< 当前测试模式 */

    std::string stdoutBuffer;   /**< 标准输出缓冲区 */
    std::string stderrBuffer;   /**< 标准错误缓冲区 */

    bool processExited;         /**< 进程是否已退出 */

};

/**
 * @brief 全局上下文类 (单例模式)
 */
class Context {
public:
    // 获取单例实例
    static Context& getInstance();

    // 禁止拷贝和赋值
    Context(const Context&) = delete;
    Context& operator=(const Context&) = delete;

    // 初始化
    bool init(int argc, char** argv);

    // 清理资源
    void cleanup();

    // 运行服务器
    int runServer();

    // 运行测试用例
    int runTest(const std::string& testFile);

    // 运行多个测试用例
    int runTests(const std::vector<std::string>& testFiles);

    // Mock函数
    int mockFunction(pid_t pid, const std::string& funcName, struct json_object* params);

    // 调用函数
    int callFunction(pid_t pid, const std::string& funcName, struct json_object* params);

    // Getters
    Config& getConfig() { return config; }
    const Config& getConfig() const { return config; }
    std::shared_ptr<UnixDomainSocket> getUnixSocket() const { return unixSocket; }
    std::shared_ptr<TcpSocket> getTcpSocket() const { return tcpSocket; }
    ClientMapType& getClients() { return clients; }
    const ClientMapType& getClients() const { return clients; }

    // 便利方法
    std::shared_ptr<ClientConnection> getClientByUUID(const std::string& uuid) const;
    std::vector<std::shared_ptr<ClientConnection>> getAllClients() const;
    void addClient(std::shared_ptr<ClientConnection> client);
    void removeClient(const std::string& uuid);
    std::shared_ptr<TestCase> getCurrentTest() const { return currentTest; }
    std::shared_ptr<TestResult> getResult() const { return result; }
    bool isRunning() const { return running; }

    // Setters
    void setUnixSocket(std::shared_ptr<UnixDomainSocket> socket) { unixSocket = socket; }
    void setTcpSocket(std::shared_ptr<TcpSocket> socket) { tcpSocket = socket; }
    void setCurrentTest(std::shared_ptr<TestCase> test) { currentTest = test; }
    void setResult(std::shared_ptr<TestResult> r) { result = r; }
    void setRunning(bool r) { running = r; }

    // 查找客户端
    std::shared_ptr<ClientConnection> findClientByProgramName(const std::string& programName);

    // 终止所有子进程（wait_attach的进程除外）
    void terminateAllChildProcesses();

private:
    // 私有构造函数
    Context();
    ~Context() = default;

    Config config;                                      /**< 配置信息 */
    std::shared_ptr<UnixDomainSocket> unixSocket;      /**< Unix Domain Socket */
    std::shared_ptr<TcpSocket> tcpSocket;              /**< TCP Socket */
    ClientMapType clients; /**< 客户端连接映射表(UUID->Connection) */
    std::shared_ptr<TestCase> currentTest;             /**< 当前测试用例 */
    std::shared_ptr<TestResult> result;                /**< 测试结果 */
    std::atomic<bool> running;                         /**< 运行状态 */

};

} // namespace testd

#endif // TESTD_CONTEXT_HPP
