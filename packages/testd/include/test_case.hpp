/**
 * @file test_case.hpp
 * @brief 测试用例管理模块头文件 - C++11版本
 */

#ifndef TESTD_TEST_CASE_HPP
#define TESTD_TEST_CASE_HPP

#include "testd.hpp"
#include "task.hpp"

// 前向声明
class MessageHandler;
class MessageFilter;

namespace testd {

/**
 * @brief 测试结果类
 */
class TestResult {
public:
    explicit TestResult(const std::string& testId);
    ~TestResult();

    // 禁止拷贝和赋值
    TestResult(const TestResult&) = delete;
    TestResult& operator=(const TestResult&) = delete;

    // 添加案例结果
    void addCaseResult(const std::string& caseName, bool passed, const std::string& message = "");

    void parseCaseListTask(std::shared_ptr<TestCaseListTask> task);

    // 设置测试结束
    void setFinished(int total, int passed, int failed, int durationMs);

    // 保存结果到文件
    bool saveToFile(const std::string& filePath) const;

    // Getters
    const std::string& getTestId() const { return testId; }
    int getTotal() const { return total; }
    int getPassed() const { return passed; }
    int getFailed() const { return failed; }
    int getDurationMs() const { return durationMs; }
    struct json_object* getCaseResults() const { return caseResults; }

    void setTestId(const std::string& id) { testId = id; }

private:
    std::string testId;         /**< 测试ID */
    int total;                  /**< 总测试数 */
    int passed;                 /**< 通过测试数 */
    int failed;                 /**< 失败测试数 */
    int durationMs;             /**< 测试持续时间(毫秒) */
    struct json_object* caseResults; /**< 各案例结果 */
};

/**
 * @brief 测试用例类
 */
class TestCase : public Task {
public:
    TestCase(const std::string& name, const std::string& description, Private);
    static inline std::shared_ptr<TestCase> create(const std::string& name, const std::string& description)
    {
        return std::make_shared<TestCase>(name, description, Private());
    }
    static std::shared_ptr<TestCase> loadFromFile(const std::string& filePath);
    virtual ~TestCase();

    // 禁止拷贝和赋值
    TestCase(const TestCase&) = delete;
    TestCase& operator=(const TestCase&) = delete;

    virtual int executeCustom() override;

    // 从文件加载测试用例

    // Getters
    const std::string& getName() const { return name; }
    const std::string& getDescription() const { return description; }
    struct json_object* getSetup() const { return setup_obj; }
    struct json_object* getCases() const { return cases_obj; }
    struct json_object* getCleanup() const { return cleanup_obj; }
    const std::string& getTestId() const { return testId; }
    std::shared_ptr<TestResult> getResult() { return result; }
    pid_t getTargetPid() const { return targetPid; }

    // Setters
    void setSetup(struct json_object* s);
    void setCases(struct json_object* c);
    void setCleanup(struct json_object* c);
    void setTargetPid(pid_t pid) { targetPid = pid; }

protected:

    // 生成唯一的测试ID
    static std::string generateTestId();

    std::string name;           /**< 测试用例名称 */
    std::string description;    /**< 测试用例描述 */
    struct json_object* setup_obj;  /**< 设置步骤 */
    struct json_object* cases_obj;  /**< 测试案例列表 */
    struct json_object* cleanup_obj; /**< 清理步骤 */
    std::string testId;         /**< 测试ID */
    std::shared_ptr<TestResult> result;
    pid_t targetPid;            /**< 目标进程ID */
    int caseState;              /**< 案例状态 */

    int sentCount = 0;

    std::weak_ptr<TestCaseListTask> casesTask;

};

} // namespace testd

#endif // TESTD_TEST_CASE_HPP
