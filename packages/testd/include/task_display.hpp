/**
 * @file task_display.hpp
 * @brief 任务显示系统头文件 - 支持动态任务树显示
 */

#ifndef TESTD_TASK_DISPLAY_HPP
#define TESTD_TASK_DISPLAY_HPP

#include "testd.hpp"
#include "task.hpp"
#include <memory>
#include <string>
#include <vector>
#include <unistd.h>

namespace testd {

/**
 * @brief 动态任务树显示器
 */
class TaskTreeDisplay {
public:
    TaskTreeDisplay();
    ~TaskTreeDisplay();
    
    // 禁止拷贝和赋值
    TaskTreeDisplay(const TaskTreeDisplay&) = delete;
    TaskTreeDisplay& operator=(const TaskTreeDisplay&) = delete;
    
    /**
     * @brief 检查是否为TTY终端
     */
    static bool isTTY();
    
    /**
     * @brief 启用/禁用动态显示
     */
    void setEnabled(bool enabled);
    bool isEnabled() const { return enabled_; }
    
    /**
     * @brief 刷新显示
     */
    void refresh(std::shared_ptr<Task> rootTask);
    
    /**
     * @brief 清除显示
     */
    void clear();
    
    /**
     * @brief 完成显示（显示最终结果）
     */
    void finalize(std::shared_ptr<Task> rootTask);
    
private:
    bool enabled_;
    bool isTTY_;
    int lastDisplayLines_;
    
    /**
     * @brief 获取状态符号
     */
    std::string getStateSymbol(Task::State state) const;
    
    /**
     * @brief 获取状态颜色代码
     */
    std::string getStateColor(Task::State state) const;
    
    /**
     * @brief 渲染单个任务项
     */
    std::string renderTaskItem(const Task& item, int depth = 0) const;
    
    /**
     * @brief 移动光标到指定行
     */
    void moveCursorUp(int lines);
    
    /**
     * @brief 清除当前行
     */
    void clearLine();
    
    /**
     * @brief 递归显示任务树
     */
    int displayTaskTree(std::shared_ptr<Task> task, int depth, const std::string& prefix = "");
    
};

} // namespace testd

#endif // TESTD_TASK_DISPLAY_HPP
