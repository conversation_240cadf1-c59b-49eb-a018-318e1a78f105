/**
 * @file task.hpp
 * @brief 任务类头文件
 */

#ifndef TESTD_TASK_HPP
#define TESTD_TASK_HPP

#include "message_system.hpp"
#include <memory>
#include <string>
#include <list>
#include <cerrno>
#include <iostream>
#include <functional>
#include <vector>
#include <chrono>
#include <json-c/json.h>

namespace testd {

/**
 * @brief 任务类
 */
class Task : public std::enable_shared_from_this<Task> {
protected:
    struct Private { explicit Private() = default; };
public:
    enum class State {
        INIT,
        RUNNING,
        FINISHED,
        ERROR
    };

    Task(const std::string& name, const std::string& description, bool isSequentialTask, bool skipFailedTasks, Private)
        : name(name), description(description), state(State::INIT), createTime(std::chrono::steady_clock::now()), timeout(std::chrono::seconds(5)), isSequentialTask(isSequentialTask), skipFailedTasks(skipFailedTasks), taskIsolated(false)
    {

    }
    static inline std::shared_ptr<Task> create(const std::string& name, const std::string& description = "", bool isSequentialTask = false, bool skipFailedTasks = false)
    {
        return std::make_shared<Task>(name, description, isSequentialTask, skipFailedTasks, Private());
    }
    std::shared_ptr<Task> getInstance()
    {
        return shared_from_this();
    }
    virtual ~Task()
    {}

    // 添加子任务
    std::shared_ptr<Task> addSubTask(const std::shared_ptr<Task>& task)
    {
        subTasks.push_back(task);
        return task;
    }

    // 禁止拷贝和赋值
    Task(const Task&) = delete;
    Task& operator=(const Task&) = delete;

    // Getters
    const std::string& getName() const { return name; }
    const std::string& getDescription() const { return description; }
    State getState() const { return state; }
    std::weak_ptr<Task> getAfterTask() const { return afterTask; }
    const std::string& getResultString() const { return resultString; }
    const std::list<std::shared_ptr<Task>>& getSubTasks() const { return subTasks; }
    std::chrono::steady_clock::time_point getCreateTime() const { return createTime; }
    std::chrono::steady_clock::time_point getStartTime() const { return startTime; }
    std::chrono::steady_clock::time_point getEndTime() const { return endTime; }
    std::chrono::steady_clock::duration getTimeout() const { return timeout; }
    bool getIsSequentialTask() const { return isSequentialTask; }
    bool getSkipFailedTasks() const { return skipFailedTasks; }
    bool getTaskIsolated() const { return taskIsolated; }

    // Setters
    void setName(const std::string& n) { name = n; }
    void setDescription(const std::string& d) { description = d; }
    void setState(State s) {
        state = s;
        if (state == State::RUNNING) {
            startTime = std::chrono::steady_clock::now();
        } else if (state == State::FINISHED) {
            endTime = std::chrono::steady_clock::now();
        }
    }
    void setAfterTask(const std::weak_ptr<Task>& task) { afterTask = task; }
    void setResultString(const std::string& s) { resultString = s; }
    void setTimeout(std::chrono::steady_clock::duration t) { timeout = t; }
    void setIsSequentialTask(bool b) { isSequentialTask = b; }
    void setSkipFailedTasks(bool b) { skipFailedTasks = b; }
    void setTaskIsolated(bool b) { taskIsolated = b; }

    // 执行任务
    virtual int execute(bool cleanupFlag = false) {
        bool allFinished = true;
        if (state == State::INIT) {
            for (auto& task : subTasks) {
                if (task->getState() == State::INIT)
                {
                    auto after = task->getAfterTask().lock();
                    if (after) {
                        if (after->getState() != State::RUNNING && after->getState() != State::FINISHED)
                        {
                            after->execute(false);
                            allFinished = allFinished && (after->getState() == State::RUNNING || after->getState() == State::FINISHED);
                            continue;
                        }
                    }
                    int ret = task->execute(false);
                    if (ret < 0) {
                        return ret;
                    }
                    if (task->getState() == State::INIT) {
                        if (isSequentialTask || taskIsolated) {
                            return -EAGAIN;
                        }
                        allFinished = false;
                    }
                }else if (taskIsolated && task->getState() == State::RUNNING) {
                    auto after = task->getAfterTask().lock();
                    if (after) {
                        if (after->getState() != State::FINISHED)
                        {
                            after->execute(false);
                            allFinished = allFinished && (after->getState() == State::FINISHED);
                            continue;
                        }
                    }
                    int ret = task->execute(cleanupFlag);
                    if (ret < 0) {
                        return ret;
                    }
                    if (task->getState() == State::RUNNING || task->getState() == State::INIT) {
                        return -EAGAIN;
                    }
                }
                if(task->getState() == State::ERROR) {
                    if (!skipFailedTasks) {
                        state = State::ERROR;
                        return -EIO;
                    }
                }
            }
            if (!allFinished) {
                return -EAGAIN;
            }
            int ret = executeCustom();
            if (ret < 0) {
                if (ret != -EAGAIN) {
                    std::cerr << "Task " << name << " init failed: " << ret << std::endl;
                    startTime = endTime = std::chrono::steady_clock::now();
                    state = State::ERROR;
                } else if (timeout.count() > 0) {
                    auto now = std::chrono::steady_clock::now();
                    if (now - createTime > timeout) {
                        std::cerr << "Task " << name << " init timeout." << std::endl;
                        startTime = endTime = std::chrono::steady_clock::now();
                        state = State::ERROR;
                        return -ETIMEDOUT;
                    }
                }
                return ret;
            }
            state = State::RUNNING;
            startTime = std::chrono::steady_clock::now();
            return 0;
        }else if(state == State::RUNNING){
            for (auto& task : subTasks) {
                if (task->getState() == State::RUNNING || task->getState() == State::INIT)
                {
                    auto after = task->getAfterTask().lock();
                    if (after) {
                        if (after->getState() != State::FINISHED)
                        {
                            after->execute(false);
                            allFinished = allFinished && (after->getState() == State::FINISHED);
                            continue;
                        }
                    }
                    int ret = task->execute(cleanupFlag);
                    if (ret < 0) {
                        return ret;
                    }
                    if (task->getState() == State::RUNNING || task->getState() == State::INIT) {
                        if (isSequentialTask || taskIsolated) {
                            return -EAGAIN;
                        }
                        allFinished = false;
                    }
                }
                if(task->getState() == State::ERROR) {
                    if (!skipFailedTasks) {
                        state = State::ERROR;
                        return -EIO;
                    }
                }
            }
            if (!allFinished) {
                return -EAGAIN;
            }
            int ret = executeCustom();
            if (ret < 0) {
                if (ret != -EAGAIN) {
                    std::cerr << "Task " << name << " running failed: " << ret << std::endl;
                    endTime = std::chrono::steady_clock::now();
                    state = State::ERROR;
                } else if (timeout.count() > 0) {
                    auto now = std::chrono::steady_clock::now();
                    if (now - startTime > timeout) {
                        std::cerr << "Task " << name << " running timeout." << std::endl;
                        endTime = std::chrono::steady_clock::now();
                        state = State::ERROR;
                        return -ETIMEDOUT;
                    }
                }
                return ret;
            }
            if (cleanupFlag) {
                for (auto it = subTasks.begin(); it != subTasks.end();) {
                    if ((*it)->getState() == State::FINISHED) {
                        (*it)->cleanup();
                        it = subTasks.erase(it);
                    } else {
                        ++it;
                    }
                }
            }
            state = State::FINISHED;
            endTime = std::chrono::steady_clock::now();
            return 0;
        }else{
            return 0;
        }
    }
    virtual int cleanup() { return 0; }
    virtual int executeCustom() {return 0; }

protected:
    std::string name;           /**< 任务名称 */
    std::string description;    /**< 任务描述 */
    std::list<std::shared_ptr<Task>> subTasks; /**< 子任务列表 */
    std::weak_ptr<Task> afterTask; /**< 前置任务 */
    State state;                /**< 任务状态 */
    std::string resultString;        /**< 任务结果 */
    std::chrono::steady_clock::time_point createTime;
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::chrono::steady_clock::duration timeout;

    bool isSequentialTask;
    bool skipFailedTasks;
    bool taskIsolated;

};

class LambdaTask : public Task {
    class LambdaMessageHandler : public MessageHandler{
        public:
            LambdaMessageHandler(class LambdaTask *task, int priority) : task_(task), priority_(priority) {}
            bool handleMessage(const EnhancedMessage& msg) override {
                task_->messages.push_back(msg);
                return true;
            }
            std::string getName() const override { return task_->getName() + "_MessageHandler"; }
            int getPriority() const override { return priority_; }
        private:
            class LambdaTask *task_;
            int priority_;
    };
public:
    using ExecuteFunction = std::function<int(class LambdaTask*,Task::State, struct json_object*)>;
    
    LambdaTask(const std::string& name, const std::string& description, 
               ExecuteFunction executeFunc, bool isSequentialTask, bool skipFailedTasks, bool taskIsolated, struct json_object* json_obj, Private)
        : Task(name, description, isSequentialTask, skipFailedTasks, Private()), executeFunc_(executeFunc), json_obj(json_obj) {
            if (json_obj) {
                json_object_get(json_obj);
            }
            struct json_object* timeoutObj;
            if (json_object_object_get_ex(json_obj, "timeout", &timeoutObj)) {
                setTimeout(std::chrono::milliseconds(json_object_get_int(timeoutObj)));
            }
            struct json_object* nameObj;
            if (json_object_object_get_ex(json_obj, "name", &nameObj)) {
                setDescription(json_object_get_string(nameObj));
            }
            setTaskIsolated(taskIsolated);
        }

    ~LambdaTask() {
        if (json_obj) {
            json_object_put(json_obj);
        }
        for (auto& subscription : subscriptions) {
            MessageSystem::getInstance().getRouter().unsubscribe(subscription);
        }
    }
    
    static std::shared_ptr<LambdaTask> create(const std::string& name, 
                                             const std::string& description,
                                             ExecuteFunction executeFunc,
                                             bool isSequentialTask = false,
                                             bool skipFailedTasks = false,
                                             bool taskIsolated = false,
                                             struct json_object* json_obj = nullptr) {
        return std::make_shared<LambdaTask>(name, description, executeFunc, isSequentialTask, skipFailedTasks, taskIsolated, json_obj, Private());
    }
    
    virtual int executeCustom() override {
        if (executeFunc_) {
            return executeFunc_(this, getState(), json_obj);
        }
        return 0;
    }

    virtual int cleanup() override {
        if (json_obj) {
            json_object_put(json_obj);
        }
        for (auto& subscription : subscriptions) {
            MessageSystem::getInstance().getRouter().unsubscribe(subscription);
        }
        subscriptions.clear();
        return 0;
    }

    std::string subscribeMessage(MessageFilter filter, int priority = 0, bool consumeMessage = true) {
        std::string handlerId = MessageSystem::getInstance().subscribe(filter, std::make_shared<LambdaMessageHandler>(this, priority), priority, consumeMessage);
        subscriptions.push_back(handlerId);
        return handlerId;
    }

    std::vector<EnhancedMessage> &getMessages() { return messages; }
    
private:
    ExecuteFunction executeFunc_;
    struct json_object* json_obj;
    std::vector<EnhancedMessage> messages;
    std::vector<std::string> subscriptions;
};

class SequentialTask : public Task {
public:
    SequentialTask(const std::string& name, const std::string& description, bool skipFailedTasks, Private)
        : Task(name, description, true, skipFailedTasks, Private()) {}
    
    static std::shared_ptr<SequentialTask> create(const std::string& name, 
                                                  const std::string& description,
                                                  bool skipFailedTasks = false) {
        return std::make_shared<SequentialTask>(name, description, skipFailedTasks, Private());
    }
};

} // namespace testd

#endif // TESTD_TASK_HPP
