/**
 * @file task_factory.hpp
 * @brief 任务工厂头文件 - 使用lambda简化任务创建
 */

#ifndef TESTD_TASK_FACTORY_HPP
#define TESTD_TASK_FACTORY_HPP

#include "task.hpp"
#include <functional>
#include <chrono>
#include <vector>
#include <memory>

namespace testd {

/**
 * @brief 任务工厂 - 提供常用任务的lambda实现
 */
class TaskFactory {
public:
    /**
     * @brief 创建等待时间任务
     */
    static std::shared_ptr<Task> createWaitTimeTask(const std::string& name, 
                                                   const std::string& description,
                                                   int waitMs);
    
    /**
     * @brief 创建等待进程连接任务
     */
    static std::shared_ptr<Task> createWaitAttachTask(const std::string& name,
                                                     const std::string& description,
                                                     std::vector<std::string> processes);
    
    /**
     * @brief 创建执行命令任务
     */
    static std::shared_ptr<Task> createExecTask(const std::string& name,
                                               const std::string& description,
                                               const std::vector<std::string>& command);
    
    /**
     * @brief 创建简单的设置任务
     */
    static std::shared_ptr<Task> createSetupTask(const std::string& name,
                                                const std::string& description,
                                                struct json_object* setupObj);
    
    static std::shared_ptr<Task> createCaseListTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj);
    static std::shared_ptr<Task> createCaseTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj);
    static std::shared_ptr<Task> createCaseRunTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj);
    /**
     * @brief 创建验证任务
     */
    static std::shared_ptr<Task> createVerifyTask(const std::string& name,
                                                 const std::string& description,
                                                 struct json_object* verifyObj);
    
    /**
     * @brief 创建自定义lambda任务
     */
    template<typename Func>
    static std::shared_ptr<Task> createCustomTask(const std::string& name,
                                                 const std::string& description,
                                                 Func&& func) {
        return LambdaTask::create(name, description, std::forward<Func>(func), true, true);
    }
    
private:
    TaskFactory() = default;
};

} // namespace testd

#endif // TESTD_TASK_FACTORY_HPP
