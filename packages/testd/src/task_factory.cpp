/**
 * @file task_factory.cpp
 * @brief 任务工厂实现 - 使用lambda简化任务创建
 */

#include "task_factory.hpp"
#include "context.hpp"
#include "message_system.hpp"
#include "message_handlers.hpp"
#include "task.hpp"
#include "test_case.hpp"
#include <chrono>
#include <thread>
#include <algorithm>
#include <sys/wait.h>
#include <unistd.h>
#include <json-c/json.h>
#include <spawn.h>
#include <fcntl.h>

namespace testd {

static std::vector<std::string> parseCommandString(const std::string& commandStr) {
    std::vector<std::string> tokens;
    std::string current;
    int state = 0; // 0: 等待状态, 1: 非引号参数, 2: 双引号参数

    for (size_t i = 0; i < commandStr.length(); i++) {
        char c = commandStr[i];
        if (state == 0) { // 等待状态（跳过空白）
            if (c == ' ' || c == '\t') {
                continue;
            } else if (c == '"') {
                state = 2; // 进入双引号模式
            } else if (c == '\\') {
                if (i + 1 < commandStr.length()) {
                    current += commandStr[++i]; // 转义下一个字符
                } else {
                    current += c; // 末尾的反斜杠
                }
                state = 1; // 进入非引号模式
            } else {
                current += c;
                state = 1; // 进入非引号模式
            }
        } else if (state == 1) { // 非引号参数
            if (c == '\\') {
                if (i + 1 < commandStr.length()) {
                    current += commandStr[++i]; // 转义下一个字符
                } else {
                    current += c; // 末尾的反斜杠
                }
            } else if (c == '"') {
                state = 2; // 进入双引号模式
            } else if (c == ' ' || c == '\t') {
                tokens.push_back(current);
                current.clear();
                state = 0; // 回到等待状态
            } else {
                current += c;
            }
        } else if (state == 2) { // 双引号参数
            if (c == '\\') {
                if (i + 1 < commandStr.length()) {
                    current += commandStr[++i]; // 转义下一个字符
                } else {
                    current += c; // 末尾的反斜杠
                }
            } else if (c == '"') {
                state = 1; // 回到非引号模式（仍在同一参数内）
            } else {
                current += c;
            }
        }
    }

    // 处理最后一个未结束的参数
    if (!current.empty() || state == 2) {
        tokens.push_back(current);
    }

    return tokens;
}



std::shared_ptr<Task> TaskFactory::createWaitTimeTask(const std::string& name, 
                                                     const std::string& description,
                                                     int waitMs) {
    auto targetTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(waitMs);
    return LambdaTask::create(name, description, [targetTime](class LambdaTask* task, Task::State state, struct json_object* json_obj) -> int {
        (void)task;
        (void)json_obj;
        if (state == Task::State::INIT) {
            return 0;
        }else if (state == Task::State::RUNNING) {
            if (std::chrono::steady_clock::now() >= targetTime) {
                return 0;
            }
        }
        return -EAGAIN;
    }, true, false, false, nullptr);
}

std::shared_ptr<Task> TaskFactory::createWaitAttachTask(const std::string& name,
                                                       const std::string& description,
                                                       std::vector<std::string> processes) {

    int process_count = processes.size();
    if (process_count == 0) {
        return nullptr;
    }
    return LambdaTask::create(name, description, [process_count, processes](class LambdaTask* task, Task::State state, struct json_object* json_obj) mutable -> int {
        (void)json_obj;
        auto &messages = task->getMessages();
        process_count -= messages.size();
        messages.clear();
        if (state == Task::State::INIT) {
            MessageFilter filter;
            filter.byType("init");
            filter.byCustom([&processes](const EnhancedMessage& msg) -> bool {
                auto client = msg.clientConnection.lock();
                if (client) {
                    struct json_object *dataObj = msg.getData();
                    if (dataObj) {
                        struct json_object* pidObj;
                        if (json_object_object_get_ex(dataObj, "pid", &pidObj)) {
                            int pid = json_object_get_int(pidObj);
                            client->setPid(pid);
                        }

                        struct json_object* programObj;
                        if (json_object_object_get_ex(dataObj, "program", &programObj)) {
                            const char* program = json_object_get_string(programObj);
                            if (program) {
                                client->setProgramName(program);
                            }
                        }
                        // 检查是否为期望的进程
                        for (const auto& expectedProcess : processes) {
                            if (client->getProgramName() == expectedProcess) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            });
            task->subscribeMessage(filter, 150);
        }else if (state == Task::State::RUNNING) {
            if (process_count == 0) {
                return 0;
            }
        }
        return -EAGAIN;
    }, true, false, false, nullptr);
}

std::shared_ptr<Task> TaskFactory::createExecTask(const std::string& name,
                                                 const std::string& description,
                                                 const std::vector<std::string>& command) {
    pid_t child_pid = -1;
    int stdout_pipe[2] = {-1, -1};
    int stderr_pipe[2] = {-1, -1};
    bool childConnected = false;

    return LambdaTask::create(name, description, [command, child_pid, stdout_pipe, stderr_pipe, childConnected](class LambdaTask *task, Task::State state, struct json_object* json_obj) mutable -> int {
        (void)task;
        (void)json_obj;
        auto &messages = task->getMessages();
        for (auto& msg : messages) {
            auto client = msg.clientConnection.lock();
            if (client) {
                childConnected = true;
                client->setIsChild(true);
                client->setPipeFd(1, stdout_pipe[0]);
                client->setPipeFd(2, stderr_pipe[0]);
                break;
            }
        }
        messages.clear();
        if (state == Task::State::INIT) {
            // 创建管道用于捕获标准输出和标准错误
            if (pipe(stdout_pipe) == -1 || pipe(stderr_pipe) == -1) {
                std::cerr << "Failed to create pipes for command: " << command.front() << std::endl;
                return -1;
            }

            std::vector<char*> argv;
            for (const auto& arg : command) {
                argv.push_back(const_cast<char*>(arg.c_str()));
            }
            argv.push_back(nullptr);

            // 构建可执行文件的完整路径
            std::string execPath = command.front();
            const std::string& sysroot = Context::getInstance().getConfig().getSysroot();
            if (!sysroot.empty() && execPath.find('/') == std::string::npos) {
                // 如果指定了sysroot且命令不包含路径分隔符，则在sysroot中查找
                execPath = sysroot + "/usr/bin/" + execPath;
            }

            // 准备环境变量
            std::vector<std::string> envStrings;
            std::vector<char*> envp;

            // 复制现有环境变量
            for (char** env = environ; *env != nullptr; ++env) {
                std::string envVar(*env);
                // 如果指定了sysroot，更新LD_LIBRARY_PATH
                if (!sysroot.empty() && envVar.find("LD_LIBRARY_PATH=") == 0) {
                    envVar = "LD_LIBRARY_PATH=" + sysroot + "/usr/bin";
                }
                envStrings.push_back(envVar);
            }

            // 如果没有LD_LIBRARY_PATH且指定了sysroot，添加它
            if (!sysroot.empty()) {
                bool hasLdLibraryPath = false;
                for (const auto& env : envStrings) {
                    if (env.find("LD_LIBRARY_PATH=") == 0) {
                        hasLdLibraryPath = true;
                        break;
                    }
                }
                if (!hasLdLibraryPath) {
                    envStrings.push_back("LD_LIBRARY_PATH=" + sysroot + "/usr/bin");
                }
            }

            // 转换为char*数组
            for (const auto& env : envStrings) {
                envp.push_back(const_cast<char*>(env.c_str()));
            }
            envp.push_back(nullptr);

            posix_spawn_file_actions_t file_actions;
            posix_spawn_file_actions_init(&file_actions);

            // 重定向标准输出和标准错误到管道
            posix_spawn_file_actions_adddup2(&file_actions, stdout_pipe[1], STDOUT_FILENO);
            posix_spawn_file_actions_adddup2(&file_actions, stderr_pipe[1], STDERR_FILENO);
            posix_spawn_file_actions_addclose(&file_actions, stdout_pipe[0]);
            posix_spawn_file_actions_addclose(&file_actions, stderr_pipe[0]);
#if defined(__GLIBC__) && (__GLIBC__ > 2 || (__GLIBC__ == 2 && __GLIBC_MINOR__ >= 34))
            posix_spawn_file_actions_addclosefrom_np(&file_actions, 3);
#else
            for (int fd = 3; fd < 1024; fd++) {
                posix_spawn_file_actions_addclose(&file_actions, fd);
            }
#endif
            posix_spawn_file_actions_addclose(&file_actions, 0);

            int status = posix_spawnp(&child_pid, execPath.c_str(), &file_actions, NULL, argv.data(), envp.data());
            posix_spawn_file_actions_destroy(&file_actions);

            if (status != 0) {
                std::cerr << "Failed to spawn process for command: " << execPath << " (errno: " << status << ")" << std::endl;
                close(stdout_pipe[0]);
                close(stdout_pipe[1]);
                close(stderr_pipe[0]);
                close(stderr_pipe[1]);
                return -1;
            }

            // 关闭子进程端的管道
            close(stdout_pipe[1]);
            close(stderr_pipe[1]);

            // 设置非阻塞模式
            fcntl(stdout_pipe[0], F_SETFL, O_NONBLOCK);
            fcntl(stderr_pipe[0], F_SETFL, O_NONBLOCK);
            stdout_pipe[1] = stderr_pipe[1] = -1;

            std::cout << "Started process (PID: " << child_pid << ") for command: " << execPath << std::endl;

            // 注册消息处理器来监听子进程的init消息
            MessageFilter filter;
            filter.byType("init");
            filter.byCustom([child_pid](const EnhancedMessage& msg) -> bool {
                auto client = msg.clientConnection.lock();
                if (client) {
                    struct json_object *dataObj = msg.getData();
                    if (dataObj) {
                        struct json_object* pidObj;
                        if (json_object_object_get_ex(dataObj, "pid", &pidObj)) {
                            int pid = json_object_get_int(pidObj);
                            client->setPid(pid);
                        }

                        struct json_object* programObj;
                        if (json_object_object_get_ex(dataObj, "program", &programObj)) {
                            const char* program = json_object_get_string(programObj);
                            if (program) {
                                client->setProgramName(program);
                            }
                        }

                        // 检查是否为期望的PID
                        if (client->getPid() == child_pid) {
                            return true;
                        }
                    }
                }
                return false;
            });
            task->subscribeMessage(filter, 150);
            return 0;
        }

        if (state == Task::State::RUNNING) {
            // 检查子进程是否已经退出
            int status;
            if (waitpid(child_pid, &status, WNOHANG) == child_pid) {
                std::cout << "Process (PID: " << child_pid << ") exited" << std::endl;
                std::cout << "Status: " << status << std::endl;
                return 0;
            }

            // 检查子进程是否已连接
            if (childConnected) {
                return 0;
            }

            return -EAGAIN;
        }

        return 0;
    }, false, false, false, nullptr);
}

std::shared_ptr<Task> TaskFactory::createSetupTask(const std::string& name,
                                                  const std::string& description,
                                                  struct json_object* setupObj) {

    auto task = LambdaTask::create(name, description, [](class LambdaTask *task, Task::State state, struct json_object* json_obj) -> int {
        (void)task;
        (void)state;
        (void)json_obj;
        return 0;
    }, true, false, false, setupObj);
        // 创建子任务
    int setup_obj_length = json_object_array_length(setupObj);
    for (int i = 0; i < setup_obj_length; i++) {
        struct json_object* setupStep = json_object_array_get_idx(setupObj, i);
        struct json_object* actionObj;
        std::weak_ptr<Task> lastTask;
        if (json_object_object_get_ex(setupStep, "action", &actionObj)) {
            std::string action = json_object_get_string(actionObj);
            if (action == "exec") {
                struct json_object* commandObj;
                if (json_object_object_get_ex(setupStep, "command", &commandObj)) {
                    std::vector<std::string> command;
                    if (json_object_is_type(commandObj, json_type_array)) {
                        int length = json_object_array_length(commandObj);
                        for (int i = 0; i < length; i++) {
                            struct json_object* commandItem = json_object_array_get_idx(commandObj, i);
                            std::string commandItemStr = json_object_get_string(commandItem);
                            command.push_back(commandItemStr);
                        }
                    } else if (json_object_is_type(commandObj, json_type_string)) {
                        std::string commandStr = json_object_get_string(commandObj);
                        command = parseCommandString(commandStr);
                    } else {
                        std::cerr << "Error: 'command' must be string or array" << std::endl;
                        continue;
                    }
                    auto t = TaskFactory::createExecTask("Exec", "", command);
                    t->setAfterTask(lastTask);
                    lastTask = task->addSubTask(t);
                }
            } else if (action == "wait") {
                int waitMs = 0;
                struct json_object* waitObj;
                if (json_object_object_get_ex(setupStep, "wait_ms", &waitObj)) {
                    waitMs = json_object_get_int(waitObj);
                }
                auto t = TestWaitTimeTask::create("Wait", "", waitMs);
                t->setAfterTask(lastTask);
                lastTask = task->addSubTask(t);
            } else if (action == "wait_attach") {
                int waitMs = 0;
                struct json_object* processesObj;
                if (json_object_object_get_ex(setupStep, "processes", &processesObj)) {
                    int length = json_object_array_length(processesObj);
                    std::vector<std::string> processes;
                    for (int i = 0; i < length; i++) {
                        struct json_object* processObj = json_object_array_get_idx(processesObj, i);
                        std::string process = json_object_get_string(processObj);
                        processes.push_back(process);
                    }
                    struct json_object* waitObj;
                    if (json_object_object_get_ex(setupStep, "wait_ms", &waitObj)) {
                        waitMs = json_object_get_int(waitObj);
                    }
                    auto t = TestWaitAttachTask::create("WaitAttach", "", processes, waitMs);
                    t->setAfterTask(lastTask);
                    lastTask = task->addSubTask(t);
                }
            }
        }
    }

    return task;
}

std::shared_ptr<Task> TaskFactory::createCaseListTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj) {
    auto task = LambdaTask::create(name, description, [](class LambdaTask *task, Task::State state, struct json_object* json_obj) -> int {
        (void)task;
        (void)state;
        (void)json_obj;
        return 0;
    }, true, false, true, caseObj);
    int casesCount = json_object_array_length(caseObj);
    std::string case_name;
    for (int i = 0; i < casesCount; i++) {
        struct json_object* caseObj = json_object_array_get_idx(caseObj, i);
        struct json_object* NameObj;
        if (json_object_object_get_ex(caseObj, "name", &NameObj)) {
            case_name = json_object_get_string(NameObj);
        }else{
            case_name = "Case " + std::to_string(i);
        }
        auto t = TestCaseTask::create(case_name, "", caseObj);
        task->addSubTask(t);
    }
    return task;

}

std::shared_ptr<Task> TaskFactory::createCaseTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj) {
    auto task = Task::create(name, description, true, false);
    struct json_object* nameObj;
    if (json_object_object_get_ex(caseObj, "name", &nameObj)) {
        task->setDescription(json_object_get_string(nameObj));
    }

    struct json_object* targetsObj;
    if (json_object_object_get_ex(caseObj, "targets", &targetsObj)) {
        int targetCount = json_object_array_length(targetsObj);
        for (int i = 0; i < targetCount; i++) {
            struct json_object* targetObj = json_object_array_get_idx(targetsObj, i);
            auto t = TestCaseRunTask::create("Run", "", targetObj);
            task->addSubTask(t);
        }
    }
    struct json_object* verifyObj;
    if (json_object_object_get_ex(caseObj, "verify", &verifyObj)) {
        auto t = TestCaseVerifyTask::create("Verify", "", verifyObj);
        task->addSubTask(t);
    }
    return task;
}

std::shared_ptr<Task> TaskFactory::createCaseRunTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj) {
    std::weak_ptr<ClientConnection> client;
    bool isRunning = false;
    auto task = LambdaTask::create(name, description, [client, isRunning](class LambdaTask *task, Task::State state, struct json_object* json_obj) mutable -> int {
        if (state == Task::State::INIT) {
            // 发送请求前，先查找client，如果没有找到，则返回-EAGAIN
            if (client.expired())
            {
                struct json_object* programObj;
                std::string programName;
                if (json_object_object_get_ex(json_obj, "process", &programObj)) {
                    programName = json_object_get_string(programObj);
                }else{
                    return -EINVAL;
                }
                client = Context::getInstance().findClientByProgramName(programName);
                if (client.expired()) {
                    return -EAGAIN;
                }
                
                // 发送aspects请求
                struct json_object* aspectsObj;
                std::vector<std::string> aspects = {};
                if (json_object_object_get_ex(json_obj, "aspects", &aspectsObj)) {
                    int aspectsCount = json_object_array_length(aspectsObj);
                    for (int i = 0; i < aspectsCount; i++) {
                        struct json_object* aspectObj = json_object_array_get_idx(aspectsObj, i);
                        std::string aspectName = json_object_get_string(aspectObj);
                        aspects.push_back(aspectName);
                    }
                    auto setAspectsTask = RequestTask::create("SetAspects", "", client, JsonProtocol::createAspectsRequest( aspects));
                    task->addSubTask(setAspectsTask);
                }


                // 发送mocks请求
                struct json_object* mocksList;
                if (json_object_object_get_ex(json_obj, "mocks", &mocksList)) {

                    struct json_object* mocksObj = json_object_new_object();
                    json_object_object_add(mocksObj, "mocks", mocksList);
                    auto mocksTask = RequestTask::create("Mocks", "", client, mocksObj);
                    json_object_put(mocksObj);
                    task->addSubTask(mocksTask);
                }

                // 发送mode请求
                auto modeTask = RequestTask::create("Mode", "", client, JsonProtocol::createSetModeRequest(TestMode::MOCK));
                task->addSubTask(modeTask);

                // 发送bkpt请求
                struct json_object* bkptListObj;
                if (json_object_object_get_ex(json_obj, "breakpoints", &bkptListObj)) {
                    struct json_object* bkptObj = json_object_new_object();
                    json_object_object_add(bkptObj, "breakpoints", bkptListObj);
                    auto bkptTask = RequestTask::create("Breakpoints", "", client, bkptObj);
                    json_object_put(bkptObj);
                    task->addSubTask(bkptTask);
                }

                return -EAGAIN;
            }
            return 0;
        }
        if (state == Task::State::RUNNING) {
            if (!isRunning) {
                isRunning = true;
                // 发送run请求
                auto actionTask = RequestTask::create("Action", "", client, JsonProtocol::createActionRequest("run"));
                task->addSubTask(actionTask);
                return -EAGAIN;
            }
            return 0;
        }
        return 0;
    }, true, false, false, caseObj);
    return task;
}

std::shared_ptr<Task> TaskFactory::createVerifyTask(const std::string& name,
                                                   const std::string& description,
                                                   struct json_object* verifyObj) {
    // 验证任务状态变量
    bool expect_debug_log = false;
    std::string log_type, log_message;
    bool has_command = false;
    std::string command;
    int expected_exit_code = 0;
    pid_t command_pid = -1;
    bool command_started = false;
    bool command_finished = false;
    int actual_exit_code = -1;
    bool logVerificationPassed = false;
    bool logVerificationFailed = false;

    // 检查是否有 expect_debug_log 配置
    struct json_object* logObj;
    if (json_object_object_get_ex(verifyObj, "expect_debug_log", &logObj)) {
        expect_debug_log = true;
        struct json_object *logTypeObj, *logMessageObj;
        if (json_object_object_get_ex(logObj, "type", &logTypeObj) &&
            json_object_object_get_ex(logObj, "message", &logMessageObj)) {
            log_type = json_object_get_string(logTypeObj);
            log_message = json_object_get_string(logMessageObj);
        }else {
            std::cerr << "Missing 'type' or 'message' field in expect_debug_log" << std::endl;
            return {};
        }
    }

    // 检查是否有 command 配置
    struct json_object* commandObj;
    if (json_object_object_get_ex(verifyObj, "command", &commandObj)) {
        has_command = true;
        command = json_object_get_string(commandObj);

        // 检查期望的退出码，默认为0
        struct json_object* exitCodeObj;
        if (json_object_object_get_ex(verifyObj, "expect_exit_code", &exitCodeObj)) {
            expected_exit_code = json_object_get_int(exitCodeObj);
        } else {
            expected_exit_code = 0;  // 默认期望成功退出
        }
    }

    // 如果既没有日志验证也没有命令验证，则报错
    if (!expect_debug_log && !has_command) {
        std::cerr << "No verification method specified (expect_debug_log or command)" << std::endl;
        return {};
    }
    return LambdaTask::create(name, description, [expect_debug_log, log_type, log_message, has_command, command, expected_exit_code, command_pid, command_started, command_finished, actual_exit_code, logVerificationPassed, logVerificationFailed](class LambdaTask *task, Task::State state, struct json_object* verifyObj) mutable -> int {
        (void)task;
        (void)verifyObj;
        auto &messages = task->getMessages();
        for (auto& msg : messages) {
            struct json_object* dataObj = msg.getData();
            if (dataObj) {
                std::string msgStr = json_object_get_string(dataObj);
                if (msgStr.find(log_message) != std::string::npos) {
                    logVerificationPassed = true;
                } else {
                    logVerificationFailed = true;
                }
            }
        }
        messages.clear();

        if (state == Task::State::INIT) {

            // 如果有日志验证，设置消息订阅
            if (expect_debug_log) {
                MessageFilter logFilter;
                logFilter.byType("log").bySubType(log_type);
                task->subscribeMessage(logFilter);
            }

            return 0;
        }

        if (state == Task::State::RUNNING) {
            // 处理命令验证
            if (has_command) {
                if (!command_started) {
                    // 启动命令执行
                    std::cout << "Executing verify command: " << command << std::endl;

                    command_pid = fork();
                    if (command_pid == 0) {
                        // 子进程：执行命令
                        execl("/bin/sh", "sh", "-c", command.c_str(), (char*)nullptr);
                        _exit(127);  // 如果execl失败
                    } else if (command_pid > 0) {
                        // 父进程：记录已启动
                        command_started = true;
                        std::cout << "Started verify command (PID: " << command_pid << ")" << std::endl;
                        return -EAGAIN;
                    } else {
                        // fork失败
                        std::cerr << "Failed to fork for verify command: " << command << std::endl;
                        return -1;
                    }
                } else if (!command_finished) {
                    // 检查命令是否完成
                    int status;
                    pid_t result = waitpid(command_pid, &status, WNOHANG);
                    if (result == command_pid) {
                        // 命令已完成
                        command_finished = true;
                        if (WIFEXITED(status)) {
                            actual_exit_code = WEXITSTATUS(status);
                            std::cout << "Verify command exited with code: " << actual_exit_code << std::endl;

                            if (actual_exit_code != expected_exit_code) {
                                std::cerr << "Command verification failed: expected exit code "
                                         << expected_exit_code << ", got " << actual_exit_code << std::endl;
                                return -1;
                            } else {
                                std::cout << "Command verification passed (exit code: " << actual_exit_code << ")" << std::endl;
                                has_command = false;
                            }
                        } else if (WIFSIGNALED(status)) {
                            int signal = WTERMSIG(status);
                            std::cerr << "Verify command terminated by signal: " << signal << std::endl;
                            return -1;
                        }
                    } else if (result == -1) {
                        std::cerr << "Error waiting for verify command" << std::endl;
                        return -1;
                    }
                }
            }

            // 处理日志验证（通过消息系统）
            if (expect_debug_log) {
                // 检查日志验证结果
                if (logVerificationFailed) {
                    // 日志验证失败
                    return -1;
                }

                if (!logVerificationPassed && has_command) {
                    // 继续等待日志验证结果
                    return -EAGAIN;
                }
                if (logVerificationPassed)
                {
                    expect_debug_log = false;
                }
            }
            if (!expect_debug_log && !has_command) {
                return 0;
            }

            return -EAGAIN;  // 继续等待
        }

        return 0;
    }, false, false, false, verifyObj);
}


} // namespace testd
