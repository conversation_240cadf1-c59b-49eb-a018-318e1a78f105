/**
 * @file task_factory.cpp
 * @brief 任务工厂实现 - 使用lambda简化任务创建
 */

#include "task_factory.hpp"
#include "context.hpp"
#include "message_system.hpp"
#include "message_handlers.hpp"
#include "task.hpp"
#include "test_case.hpp"
#include <chrono>
#include <thread>
#include <algorithm>
#include <sys/wait.h>
#include <unistd.h>
#include <json-c/json.h>

namespace testd {

static std::vector<std::string> parseCommandString(const std::string& commandStr) {
    std::vector<std::string> tokens;
    std::string current;
    int state = 0; // 0: 等待状态, 1: 非引号参数, 2: 双引号参数

    for (size_t i = 0; i < commandStr.length(); i++) {
        char c = commandStr[i];
        if (state == 0) { // 等待状态（跳过空白）
            if (c == ' ' || c == '\t') {
                continue;
            } else if (c == '"') {
                state = 2; // 进入双引号模式
            } else if (c == '\\') {
                if (i + 1 < commandStr.length()) {
                    current += commandStr[++i]; // 转义下一个字符
                } else {
                    current += c; // 末尾的反斜杠
                }
                state = 1; // 进入非引号模式
            } else {
                current += c;
                state = 1; // 进入非引号模式
            }
        } else if (state == 1) { // 非引号参数
            if (c == '\\') {
                if (i + 1 < commandStr.length()) {
                    current += commandStr[++i]; // 转义下一个字符
                } else {
                    current += c; // 末尾的反斜杠
                }
            } else if (c == '"') {
                state = 2; // 进入双引号模式
            } else if (c == ' ' || c == '\t') {
                tokens.push_back(current);
                current.clear();
                state = 0; // 回到等待状态
            } else {
                current += c;
            }
        } else if (state == 2) { // 双引号参数
            if (c == '\\') {
                if (i + 1 < commandStr.length()) {
                    current += commandStr[++i]; // 转义下一个字符
                } else {
                    current += c; // 末尾的反斜杠
                }
            } else if (c == '"') {
                state = 1; // 回到非引号模式（仍在同一参数内）
            } else {
                current += c;
            }
        }
    }

    // 处理最后一个未结束的参数
    if (!current.empty() || state == 2) {
        tokens.push_back(current);
    }

    return tokens;
}



std::shared_ptr<Task> TaskFactory::createWaitTimeTask(const std::string& name, 
                                                     const std::string& description,
                                                     int waitMs) {
    auto targetTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(waitMs);
    return LambdaTask::create(name, description, [targetTime](class LambdaTask* task, Task::State state, struct json_object* json_obj) -> int {
        (void)task;
        (void)json_obj;
        if (state == Task::State::INIT) {
            return 0;
        }else if (state == Task::State::RUNNING) {
            if (std::chrono::steady_clock::now() >= targetTime) {
                return 0;
            }
        }
        return -EAGAIN;
    }, true, false, false, nullptr);
}

std::shared_ptr<Task> TaskFactory::createWaitAttachTask(const std::string& name,
                                                       const std::string& description,
                                                       std::vector<std::string> processes) {

    int process_count = processes.size();
    if (process_count == 0) {
        return nullptr;
    }
    return LambdaTask::create(name, description, [process_count, processes](class LambdaTask* task, Task::State state, struct json_object* json_obj) mutable -> int {
        (void)json_obj;
        auto &messages = task->getMessages();
        process_count -= messages.size();
        messages.clear();
        if (state == Task::State::INIT) {
            MessageFilter filter;
            filter.byType("init");
            filter.byCustom([&processes](const EnhancedMessage& msg) -> bool {
                auto client = msg.clientConnection.lock();
                if (client) {
                    struct json_object *dataObj = msg.getData();
                    if (dataObj) {
                        struct json_object* pidObj;
                        if (json_object_object_get_ex(dataObj, "pid", &pidObj)) {
                            int pid = json_object_get_int(pidObj);
                            client->setPid(pid);
                        }

                        struct json_object* programObj;
                        if (json_object_object_get_ex(dataObj, "program", &programObj)) {
                            const char* program = json_object_get_string(programObj);
                            if (program) {
                                client->setProgramName(program);
                            }
                        }
                        // 检查是否为期望的进程
                        for (const auto& expectedProcess : processes) {
                            if (client->getProgramName() == expectedProcess) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            });
            task->subscribeMessage(filter, 150);
        }else if (state == Task::State::RUNNING) {
            if (process_count == 0) {
                return 0;
            }
        }
        return -EAGAIN;
    }, true, false, false, nullptr);
}

std::shared_ptr<Task> TaskFactory::createExecTask(const std::string& name,
                                                 const std::string& description,
                                                 const std::vector<std::string>& command) {
    // 简化实现
    (void)command;  // 避免未使用警告

    return LambdaTask::create(name, description, [](class LambdaTask *task, Task::State state, struct json_object* json_obj) -> int {
        if (state == Task::State::INIT) {
        }
            
    }, false, false, false, nullptr);
}

std::shared_ptr<Task> TaskFactory::createSetupTask(const std::string& name,
                                                  const std::string& description,
                                                  struct json_object* setupObj) {

    auto task = LambdaTask::create(name, description, [](class LambdaTask *task, Task::State state, struct json_object* json_obj) -> int {
        (void)task;
        (void)state;
        (void)json_obj;
        return 0;
    }, true, false, false, setupObj);
        // 创建子任务
    int setup_obj_length = json_object_array_length(setupObj);
    for (int i = 0; i < setup_obj_length; i++) {
        struct json_object* setupStep = json_object_array_get_idx(setupObj, i);
        struct json_object* actionObj;
        std::weak_ptr<Task> lastTask;
        if (json_object_object_get_ex(setupStep, "action", &actionObj)) {
            std::string action = json_object_get_string(actionObj);
            if (action == "exec") {
                struct json_object* commandObj;
                if (json_object_object_get_ex(setupStep, "command", &commandObj)) {
                    std::vector<std::string> command;
                    if (json_object_is_type(commandObj, json_type_array)) {
                        int length = json_object_array_length(commandObj);
                        for (int i = 0; i < length; i++) {
                            struct json_object* commandItem = json_object_array_get_idx(commandObj, i);
                            std::string commandItemStr = json_object_get_string(commandItem);
                            command.push_back(commandItemStr);
                        }
                    } else if (json_object_is_type(commandObj, json_type_string)) {
                        std::string commandStr = json_object_get_string(commandObj);
                        command = parseCommandString(commandStr);
                    } else {
                        std::cerr << "Error: 'command' must be string or array" << std::endl;
                        continue;
                    }
                    auto t = TestExecTask::create("Exec", "", command);
                    t->setAfterTask(lastTask);
                    lastTask = task->addSubTask(t);
                }
            } else if (action == "wait") {
                int waitMs = 0;
                struct json_object* waitObj;
                if (json_object_object_get_ex(setupStep, "wait_ms", &waitObj)) {
                    waitMs = json_object_get_int(waitObj);
                }
                auto t = TestWaitTimeTask::create("Wait", "", waitMs);
                t->setAfterTask(lastTask);
                lastTask = task->addSubTask(t);
            } else if (action == "wait_attach") {
                int waitMs = 0;
                struct json_object* processesObj;
                if (json_object_object_get_ex(setupStep, "processes", &processesObj)) {
                    int length = json_object_array_length(processesObj);
                    std::vector<std::string> processes;
                    for (int i = 0; i < length; i++) {
                        struct json_object* processObj = json_object_array_get_idx(processesObj, i);
                        std::string process = json_object_get_string(processObj);
                        processes.push_back(process);
                    }
                    struct json_object* waitObj;
                    if (json_object_object_get_ex(setupStep, "wait_ms", &waitObj)) {
                        waitMs = json_object_get_int(waitObj);
                    }
                    auto t = TestWaitAttachTask::create("WaitAttach", "", processes, waitMs);
                    t->setAfterTask(lastTask);
                    lastTask = task->addSubTask(t);
                }
            }
        }
    }

    return task;
}

std::shared_ptr<Task> TaskFactory::createCaseListTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj) {
    auto task = LambdaTask::create(name, description, [](class LambdaTask *task, Task::State state, struct json_object* json_obj) -> int {
        (void)task;
        (void)state;
        (void)json_obj;
        return 0;
    }, true, false, true, caseObj);
    int casesCount = json_object_array_length(caseObj);
    std::string case_name;
    for (int i = 0; i < casesCount; i++) {
        struct json_object* caseObj = json_object_array_get_idx(caseObj, i);
        struct json_object* NameObj;
        if (json_object_object_get_ex(caseObj, "name", &NameObj)) {
            case_name = json_object_get_string(NameObj);
        }else{
            case_name = "Case " + std::to_string(i);
        }
        auto t = TestCaseTask::create(case_name, "", caseObj);
        task->addSubTask(t);
    }
    return task;

}

std::shared_ptr<Task> TaskFactory::createCaseTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj) {
    auto task = Task::create(name, description, true, false);
    struct json_object* nameObj;
    if (json_object_object_get_ex(caseObj, "name", &nameObj)) {
        task->setDescription(json_object_get_string(nameObj));
    }

    struct json_object* targetsObj;
    if (json_object_object_get_ex(caseObj, "targets", &targetsObj)) {
        int targetCount = json_object_array_length(targetsObj);
        for (int i = 0; i < targetCount; i++) {
            struct json_object* targetObj = json_object_array_get_idx(targetsObj, i);
            auto t = TestCaseRunTask::create("Run", "", targetObj);
            task->addSubTask(t);
        }
    }
    struct json_object* verifyObj;
    if (json_object_object_get_ex(caseObj, "verify", &verifyObj)) {
        auto t = TestCaseVerifyTask::create("Verify", "", verifyObj);
        task->addSubTask(t);
    }
    return task;
}

std::shared_ptr<Task> TaskFactory::createCaseRunTask(const std::string& name,
                                                    const std::string& description,
                                                    struct json_object* caseObj) {
    std::weak_ptr<ClientConnection> client;
    bool isRunning = false;
    auto task = LambdaTask::create(name, description, [client, isRunning](class LambdaTask *task, Task::State state, struct json_object* json_obj) mutable -> int {
        if (state == Task::State::INIT) {
            // 发送请求前，先查找client，如果没有找到，则返回-EAGAIN
            if (client.expired())
            {
                struct json_object* programObj;
                std::string programName;
                if (json_object_object_get_ex(json_obj, "process", &programObj)) {
                    programName = json_object_get_string(programObj);
                }else{
                    return -EINVAL;
                }
                client = Context::getInstance().findClientByProgramName(programName);
                if (client.expired()) {
                    return -EAGAIN;
                }
                
                // 发送aspects请求
                struct json_object* aspectsObj;
                std::vector<std::string> aspects = {};
                if (json_object_object_get_ex(json_obj, "aspects", &aspectsObj)) {
                    int aspectsCount = json_object_array_length(aspectsObj);
                    for (int i = 0; i < aspectsCount; i++) {
                        struct json_object* aspectObj = json_object_array_get_idx(aspectsObj, i);
                        std::string aspectName = json_object_get_string(aspectObj);
                        aspects.push_back(aspectName);
                    }
                    auto setAspectsTask = RequestTask::create("SetAspects", "", client, JsonProtocol::createAspectsRequest( aspects));
                    task->addSubTask(setAspectsTask);
                }


                // 发送mocks请求
                struct json_object* mocksList;
                if (json_object_object_get_ex(json_obj, "mocks", &mocksList)) {

                    struct json_object* mocksObj = json_object_new_object();
                    json_object_object_add(mocksObj, "mocks", mocksList);
                    auto mocksTask = RequestTask::create("Mocks", "", client, mocksObj);
                    json_object_put(mocksObj);
                    task->addSubTask(mocksTask);
                }

                // 发送mode请求
                auto modeTask = RequestTask::create("Mode", "", client, JsonProtocol::createSetModeRequest(TestMode::MOCK));
                task->addSubTask(modeTask);

                // 发送bkpt请求
                struct json_object* bkptListObj;
                if (json_object_object_get_ex(json_obj, "breakpoints", &bkptListObj)) {
                    struct json_object* bkptObj = json_object_new_object();
                    json_object_object_add(bkptObj, "breakpoints", bkptListObj);
                    auto bkptTask = RequestTask::create("Breakpoints", "", client, bkptObj);
                    json_object_put(bkptObj);
                    task->addSubTask(bkptTask);
                }

                return -EAGAIN;
            }
            return 0;
        }
        if (state == Task::State::RUNNING) {
            if (!isRunning) {
                isRunning = true;
                // 发送run请求
                auto actionTask = RequestTask::create("Action", "", client, JsonProtocol::createActionRequest("run"));
                task->addSubTask(actionTask);
                return -EAGAIN;
            }
            return 0;
        }
        return 0;
    }, true, false, false, caseObj);
    return task;
}

std::shared_ptr<Task> TaskFactory::createVerifyTask(const std::string& name,
                                                   const std::string& description,
                                                   struct json_object* verifyObj) {
    (void)verifyObj;  // 避免未使用警告

    return LambdaTask::create(name, description, [](class LambdaTask *task, Task::State state, struct json_object* json_obj) -> int {
        // TODO
        return 0;
    });
}


} // namespace testd
