/**
 * @file task_display.cpp
 * @brief 任务显示系统实现 - 支持动态任务树显示
 */

#include "task_display.hpp"
#include "task.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>

namespace testd {

TaskTreeDisplay::TaskTreeDisplay() 
    : enabled_(false), isTTY_(isTTY()), lastDisplayLines_(0) {
}

TaskTreeDisplay::~TaskTreeDisplay() {
}

bool TaskTreeDisplay::isTTY() {
    return isatty(STDOUT_FILENO) == 1;
}

void TaskTreeDisplay::setEnabled(bool enabled) {
    enabled_ = enabled && isTTY_;
}

std::string TaskTreeDisplay::getStateSymbol(Task::State state) const {
    switch (state) {
        case Task::State::INIT:
            return "[I]";
        case Task::State::RUNNING:
            return "[R]";
        case Task::State::FINISHED:
            return "[F]";
        case Task::State::ERROR:
            return "[E]";
        default:
            return "[?]";
    }
}

std::string TaskTreeDisplay::getStateColor(Task::State state) const {
    if (!isTTY_) return "";
    
    switch (state) {
        case Task::State::INIT:
            return "\033[33m";  // 黄色
        case Task::State::RUNNING:
            return "\033[36m";  // 青色
        case Task::State::FINISHED:
            return "\033[32m";  // 绿色
        case Task::State::ERROR:
            return "\033[31m";  // 红色
        default:
            return "\033[0m";   // 重置
    }
}


std::string TaskTreeDisplay::renderTaskItem(const Task& item, int depth) const {
    std::ostringstream oss;
    
    // 添加缩进
    int total_indent = 5 * depth - 1;
    if (total_indent > 0) {
        for (int i = 0; i < total_indent; ++i) {
            oss << " ";
        }
    }
    
    // 添加树形结构符号
    if (depth > 0) {
        oss << "+";
    }
    
    // 添加状态符号和颜色
    oss << getStateColor(item.getState()) << getStateSymbol(item.getState()) << " ";
    
    // 添加任务名称
    oss << item.getName();
    
    // 添加描述（如果有）
    if (!item.getDescription().empty()) {
        oss << " - " << item.getDescription();
    }
    
    // 添加时间信息
    if (item.getState() == Task::State::RUNNING) {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - item.getStartTime());
        oss << " (" << duration.count() << "s)";
    } else if (item.getState() == Task::State::FINISHED || item.getState() == Task::State::ERROR) {
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(item.getEndTime() - item.getStartTime());
        oss << " (" << duration.count() << "ms)";
    }
    
    // 添加错误信息
    if (item.getState() == Task::State::ERROR && !item.getResultString().empty()) {
        oss << " - " << item.getResultString();
    }
    
    // 重置颜色
    if (isTTY_) {
        oss << "\033[0m";
    }
    
    return oss.str();
}

void TaskTreeDisplay::refresh(std::shared_ptr<Task> rootTask) {
    if (!enabled_ || !isTTY_) {
        return;
    }
    
    if (lastDisplayLines_ > 0) {
        // moveCursorUp(lastDisplayLines_);
        // for (int i = 0; i < lastDisplayLines_; ++i) {
        //     clearLine();
        //     std::cout << std::endl;
        // }
        moveCursorUp(lastDisplayLines_);
    }
    
    // 渲染新的显示
    int lineCount = 0;
    lineCount = displayTaskTree(rootTask, 0);
    
    lastDisplayLines_ = lineCount;
    std::cout.flush();
}

int TaskTreeDisplay::displayTaskTree(std::shared_ptr<Task> task, int depth, const std::string& prefix) {
    if (!task) {
        return 0;
    }
    
    clearLine();
    std::cout << prefix << renderTaskItem(*task, depth) << std::endl;
    
    int lineCount = 1;

    if (task->getState() == Task::State::FINISHED) {
        return lineCount;
    }
    for (const auto& subTask : task->getSubTasks()) {
        lineCount += displayTaskTree(subTask, depth + 1, prefix);
    }
    
    return lineCount;
}

void TaskTreeDisplay::clear() {
    if (enabled_ && isTTY_ && lastDisplayLines_ > 0) {
        for (int i = 0; i < lastDisplayLines_; ++i) {
            moveCursorUp(1);
            clearLine();
        }
        lastDisplayLines_ = 0;
    }
}

void TaskTreeDisplay::finalize(std::shared_ptr<Task> rootTask) {
    if (!enabled_) {
        // 非TTY模式，输出简单的结果摘要
        int completed = 0, failed = 0, total = 0;
        if (rootTask) {
            total = rootTask->getSubTasks().size();
            for (const auto& task : rootTask->getSubTasks()) {
                if (task->getState() == Task::State::FINISHED) completed++;
                else if (task->getState() == Task::State::ERROR) failed++;
            }
        }
        std::cout << "\n=== Task Summary ===" << std::endl;
        std::cout << "Total: " << total << ", Completed: " << completed 
                  << ", Failed: " << failed << std::endl;
    } else {
        // TTY模式，保持最终显示状态
        refresh(rootTask);
        clearLine();
        std::cout << std::endl;
        clearLine();
    }
}

void TaskTreeDisplay::moveCursorUp(int lines) {
    if (isTTY_ && lines > 0) {
        std::cout << "\033[" << lines << "A";
        std::cout.flush();
    }
}

void TaskTreeDisplay::clearLine() {
    if (isTTY_) {
        std::cout << "\033[2K\r";
        std::cout.flush();
    }
}

} // namespace testd
