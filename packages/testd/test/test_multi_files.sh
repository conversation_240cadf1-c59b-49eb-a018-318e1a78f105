#!/bin/bash

# 测试多文件运行功能

echo "=== 测试testd多文件运行功能 ==="

# 创建测试目录
TEST_DIR="test_cases"
mkdir -p $TEST_DIR

# 创建测试用例文件1
cat > $TEST_DIR/test1.json << 'EOF'
{
    "name": "Test Case 1",
    "description": "第一个测试用例",
    "setup": [
        {
            "action": "wait",
            "wait_ms": 100
        }
    ],
    "cases": [
        {
            "name": "Simple Test",
            "targets": [
                {
                    "program": "echo",
                    "breakpoints": ["main"]
                }
            ],
            "verify": {
                "exit_code": 0
            }
        }
    ]
}
EOF

# 创建测试用例文件2
cat > $TEST_DIR/test2.json << 'EOF'
{
    "name": "Test Case 2", 
    "description": "第二个测试用例",
    "setup": [
        {
            "action": "wait",
            "wait_ms": 200
        }
    ],
    "cases": [
        {
            "name": "Another Test",
            "targets": [
                {
                    "program": "echo",
                    "breakpoints": ["main"]
                }
            ],
            "verify": {
                "exit_code": 0
            }
        }
    ]
}
EOF

# 创建测试用例文件3
cat > $TEST_DIR/test3.json << 'EOF'
{
    "name": "Test Case 3",
    "description": "第三个测试用例", 
    "setup": [
        {
            "action": "wait",
            "wait_ms": 150
        }
    ],
    "cases": [
        {
            "name": "Final Test",
            "targets": [
                {
                    "program": "echo",
                    "breakpoints": ["main"]
                }
            ],
            "verify": {
                "exit_code": 0
            }
        }
    ]
}
EOF

echo "创建了3个测试用例文件"

# 编译testd
echo "编译testd..."
make clean && make

if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

echo "编译成功"

# 测试单个文件运行（向后兼容）
echo ""
echo "--- 测试单个文件运行 ---"
./bin/testd run $TEST_DIR/test1.json

# 测试多个文件运行
echo ""
echo "--- 测试多个文件运行 ---"
./bin/testd run $TEST_DIR/test1.json $TEST_DIR/test2.json $TEST_DIR/test3.json

# 测试通配符
echo ""
echo "--- 测试通配符运行 ---"
./bin/testd run $TEST_DIR/*.json

# 清理
echo ""
echo "清理测试文件..."
rm -rf $TEST_DIR

echo "=== 测试完成 ==="
