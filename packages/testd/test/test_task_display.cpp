/**
 * @file test_task_display.cpp
 * @brief 测试任务显示系统
 */

#include "task_display.hpp"
#include "task.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace testd;

int main() {
    std::cout << "=== 任务显示系统测试 ===" << std::endl;
    
    // 1. 测试TTY检测
    std::cout << "TTY检测: " << (TaskTreeDisplay::isTTY() ? "是TTY" : "非TTY") << std::endl;
    
    // 2. 初始化显示管理器
    TaskTreeDisplay display;
    display.setEnabled(true);
    
    std::cout << "显示器状态: " << (display.isEnabled() ? "已启用" : "已禁用") << std::endl;
    
    // 3. 创建测试任务树
    auto rootTask = Task::create("Root Task", "主任务");
    auto setupTask = Task::create("Setup", "设置任务");
    auto testTask = Task::create("Test Cases", "测试用例");
    auto cleanupTask = Task::create("Cleanup", "清理任务");
    auto cleanupTask1 = Task::create("Cleanup", "清理任务");
    
    rootTask->addSubTask(setupTask);
    rootTask->addSubTask(testTask);
    rootTask->addSubTask(cleanupTask);
    rootTask->addSubTask(cleanupTask1);
    
    std::cout << "\n--- 模拟任务执行 ---" << std::endl;
    
    // 5. 模拟任务执行过程
    // 开始setup
    setupTask->setState(Task::State::RUNNING);
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    display.refresh(rootTask);
    
    // 完成setup
    setupTask->setState(Task::State::FINISHED);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    display.refresh(rootTask);
    
    // 开始test
    testTask->setState(Task::State::RUNNING);
    std::this_thread::sleep_for(std::chrono::milliseconds(1500));
    display.refresh(rootTask);
    
    // 完成test
    testTask->setState(Task::State::FINISHED);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    display.refresh(rootTask);
    
    // 开始cleanup
    cleanupTask->setState(Task::State::RUNNING);
    std::this_thread::sleep_for(std::chrono::milliseconds(800));
    display.refresh(rootTask);
    
    // 完成cleanup
    cleanupTask->setState(Task::State::FINISHED);

    display.clear();
    
    std::cout << "\n--- 任务执行完成 ---" << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    
    // 6. 完成显示
    display.finalize(rootTask);
    
    // 7. 清理
    
    std::cout << "=== 测试完成 ===" << std::endl;
    return 0;
}
