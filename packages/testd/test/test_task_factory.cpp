/**
 * @file test_task_factory.cpp
 * @brief 测试任务工厂和lambda任务
 */

#include "task_factory.hpp"
#include "task_display.hpp"
#include <iostream>
#include <chrono>
#include <thread>

using namespace testd;

int main() {
    std::cout << "=== 任务工厂测试 ===" << std::endl;
    
    // 1. 初始化显示系统
    TaskTreeDisplay display;
    display.setEnabled(true);
    
    // 2. 测试计时器任务
    std::cout << "\n--- 测试计时器任务 ---" << std::endl;
    auto timerTask = TaskStateManager::createTimerTask(
        "Timer Test", 
        "等待1秒", 
        std::chrono::milliseconds(1000)
    );
    
    display.addTask("timer", "Timer Test", "等待1秒");
    
    timerTask->setState(Task::State::RUNNING);
    
    // 执行计时器任务
    int result = -EAGAIN;
    while (result == -EAGAIN) {
        result = timerTask->execute();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    if (result == 0) {
        timerTask->setState(Task::State::FINISHED);
        std::cout << "计时器任务完成" << std::endl;
    } else {
        timerTask->setState(Task::State::ERROR);
        std::cout << "计时器任务失败: " << result << std::endl;
    }
    
    // 3. 测试条件任务
    std::cout << "\n--- 测试条件任务 ---" << std::endl;
    
    bool condition = false;
    auto conditionTask = TaskStateManager::createConditionTask(
        "Condition Test",
        "等待条件满足",
        [&condition]() { return condition; },
        std::chrono::milliseconds(2000)
    );
    
    display.addTask("condition", "Condition Test", "等待条件满足");
    
    conditionTask->setState(Task::State::RUNNING);
    
    // 在另一个线程中设置条件
    std::thread conditionSetter([&condition]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(800));
        condition = true;
    });
    
    // 执行条件任务
    result = -EAGAIN;
    while (result == -EAGAIN) {
        result = conditionTask->execute();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    conditionSetter.join();
    
    if (result == 0) {
        conditionTask->setState(Task::State::FINISHED);
        std::cout << "条件任务完成" << std::endl;
    } else {
        conditionTask->setState(Task::State::ERROR);
        std::cout << "条件任务失败: " << result << std::endl;
    }
    
    // 4. 测试自定义lambda任务
    std::cout << "\n--- 测试自定义lambda任务 ---" << std::endl;
    
    int counter = 0;
    auto customTask = TaskFactory::createCustomTask(
        "Custom Task",
        "自定义计数任务",
        [&counter]() mutable -> int {
            counter++;
            if (counter >= 3) {
                return 0; // 完成
            }
            return -EAGAIN; // 继续
        }
    );
    
    display.addTask("custom", "Custom Task", "自定义计数任务");
    
    customTask->setState(Task::State::RUNNING);
    
    // 执行自定义任务
    result = -EAGAIN;
    while (result == -EAGAIN) {
        result = customTask->execute();
        std::cout << "计数器: " << counter << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
    }
    
    if (result == 0) {
        customTask->setState(Task::State::FINISHED);
        std::cout << "自定义任务完成，最终计数: " << counter << std::endl;
    } else {
        customTask->setState(Task::State::ERROR);
        std::cout << "自定义任务失败: " << result << std::endl;
    }
    
    // 5. 测试状态管理任务
    std::cout << "\n--- 测试状态管理任务 ---" << std::endl;
    
    struct TaskState {
        int step = 0;
        std::string message;
    };
    
    auto statefulTask = TaskStateManager::createStatefulTask<TaskState>(
        "Stateful Task",
        "带状态的任务",
        TaskState{},
        [](TaskState& state) -> int {
            state.step++;
            state.message = "Step " + std::to_string(state.step);
            
            if (state.step >= 5) {
                return 0; // 完成
            }
            return -EAGAIN; // 继续
        }
    );
    
    display.addTask("stateful", "Stateful Task", "带状态的任务");
    
    statefulTask->setState(Task::State::RUNNING);
    
    // 执行状态任务
    result = -EAGAIN;
    while (result == -EAGAIN) {
        result = statefulTask->execute();
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    if (result == 0) {
        statefulTask->setState(Task::State::FINISHED);
        std::cout << "状态任务完成" << std::endl;
    } else {
        statefulTask->setState(Task::State::ERROR);
        std::cout << "状态任务失败: " << result << std::endl;
    }
    
    // 6. 完成显示
    display.finalize();
    
    std::cout << "\n=== 任务工厂测试完成 ===" << std::endl;
    return 0;
}
