#include "message_system.hpp"
#include <iostream>
#include <memory>
#include <vector>

using namespace testd;

class TestHandler : public MessageHandler {
private:
    std::string name_;
    int priority_;
    
public:
    TestHandler(const std::string& name, int priority) 
        : name_(name), priority_(priority) {}
    
    bool handleMessage(const EnhancedMessage& msg) override {
        std::cout << "Handler [" << name_ << "] (priority=" << priority_ 
                  << ") processed message: " << msg.type << std::endl;
        return false; // 不消费消息，让其他处理器也能处理
    }
    
    std::string getName() const override {
        return name_;
    }
    
    int getPriority() const override {
        return priority_;
    }
};

int main() {
    std::cout << "=== 测试消息优先级处理修复 ===" << std::endl;
    
    auto& messageSystem = MessageSystem::getInstance();
    
    // 创建多个具有相同优先级的处理器
    auto handler1 = std::make_shared<TestHandler>("Handler1", 10);
    auto handler2 = std::make_shared<TestHandler>("Handler2", 10);
    auto handler3 = std::make_shared<TestHandler>("Handler3", 20);
    auto handler4 = std::make_shared<TestHandler>("Handler4", 10);
    auto handler5 = std::make_shared<TestHandler>("Handler5", 20);
    
    // 订阅消息
    MessageFilter filter;
    filter.byType("test");
    
    std::string sub1 = messageSystem.subscribe(filter, handler1, 10, false);
    std::string sub2 = messageSystem.subscribe(filter, handler2, 10, false);
    std::string sub3 = messageSystem.subscribe(filter, handler3, 20, false);
    std::string sub4 = messageSystem.subscribe(filter, handler4, 10, false);
    std::string sub5 = messageSystem.subscribe(filter, handler5, 20, false);
    
    std::cout << "订阅ID: " << sub1 << ", " << sub2 << ", " << sub3 << ", " << sub4 << ", " << sub5 << std::endl;
    
    // 创建测试消息
    EnhancedMessage testMsg;
    testMsg.type = "test";
    testMsg.priority = MessagePriority::NORMAL;
    testMsg.source = MessageSource::INTERNAL;
    
    // 发布消息
    std::cout << "\n发布测试消息..." << std::endl;
    messageSystem.publish(testMsg);
    
    // 处理消息
    std::cout << "\n处理消息（应该按优先级顺序：20, 20, 10, 10, 10）:" << std::endl;
    messageSystem.processMessages();
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    
    return 0;
}
